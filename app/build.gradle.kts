import java.util.TimeZone
import java.util.Properties
import java.io.FileInputStream
import java.text.SimpleDateFormat
import java.util.Locale


plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}


fun releaseTime(): String {
    val formatter = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
    formatter.timeZone = TimeZone.getTimeZone("GMT+8")
    return formatter.format(System.currentTimeMillis())
}

fun buildTime(): String {
    val formatter = SimpleDateFormat("dd-MM-yyyy HH:mm:ss", Locale.getDefault())
    formatter.timeZone = TimeZone.getTimeZone("GMT+8")
    return formatter.format(System.currentTimeMillis())
}

// Load signing properties
val signingPropsFile = file("../signing.properties")
val signingProps = Properties()
if (signingPropsFile.exists()) {
    signingProps.load(FileInputStream(signingPropsFile))
}

android {
    namespace = "com.example.in_tts_hindi_pack_injector"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.in_tts_hindi_pack_injector"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0.0_${releaseTime()}"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        val currentDateFormatter = SimpleDateFormat("ddMMYYYY", Locale.getDefault())
        currentDateFormatter.timeZone = TimeZone.getTimeZone("GMT+8")
        val currentDate = currentDateFormatter.format(System.currentTimeMillis())
        buildConfigField("String", "VERSION_DATE", "\"${currentDate}\"")

        ndk {
            abiFilters += listOf("armeabi-v7a")
        }
    }

    signingConfigs {
        getByName("debug") {
            // Debug signing config will use default debug keystore
        }
        create("release") {
            if (signingProps.containsKey("RELEASE_STORE_FILE") &&
                signingProps.containsKey("RELEASE_STORE_PASSWORD") &&
                signingProps.containsKey("RELEASE_KEY_ALIAS") &&
                signingProps.containsKey("RELEASE_KEY_PASSWORD")
            ) {

                storeFile = file(signingProps["RELEASE_STORE_FILE"] as String)
                storePassword = signingProps["RELEASE_STORE_PASSWORD"] as String
                keyAlias = signingProps["RELEASE_KEY_ALIAS"] as String
                keyPassword = signingProps["RELEASE_KEY_PASSWORD"] as String
            }
        }
    }

    buildTypes {
        debug {
            buildConfigField("Boolean", "RELEASE", "false")
            isMinifyEnabled = false
            isShrinkResources = false
            signingConfig = signingConfigs.getByName("debug")
        }

        release {
            buildConfigField("Boolean", "RELEASE", "true")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }

    android.applicationVariants.all {
        val variant = this
        outputs.all {
            val output = this
            val outputImpl = output as com.android.build.gradle.internal.api.BaseVariantOutputImpl
            val baseName = "InTtsHindiVoiceInjector"
            val versionName = variant.versionName
            val buildType = variant.buildType.name

            outputImpl.outputFileName = "${baseName}_v${versionName}_${buildType}.apk"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }

    lint {
        abortOnError = false
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.work.runtime.ktx)
    implementation(files("libs/NeptuneLiteApi_V4.15.00_T_20250522.jar"))
    implementation(libs.protolite.well.known.types)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}