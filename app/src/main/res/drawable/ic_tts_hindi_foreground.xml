<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Background circle with Indian flag colors gradient -->
    <path
        android:pathData="M54,54m-45,0a45,45 0,1 1,90 0a45,45 0,1 1,-90 0"
        android:fillColor="#FF9933"
        android:fillAlpha="0.1"/>
    
    <!-- Microphone icon (Material Design style) -->
    <group
        android:translateX="54"
        android:translateY="54">
        
        <!-- Microphone body -->
        <path
            android:pathData="M-6,-12L6,-12A6,6 0,0 1,12 -6L12,6A6,6 0,0 1,6 12L-6,12A6,6 0,0 1,-12 6L-12,-6A6,6 0,0 1,-6 -12z"
            android:fillColor="#FF9933"/>
        
        <!-- Microphone grille lines -->
        <path
            android:pathData="M-8,-6L8,-6"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="1.5"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M-8,-2L8,-2"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="1.5"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M-8,2L8,2"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="1.5"
            android:strokeLineCap="round"/>
        <path
            android:pathData="M-8,6L8,6"
            android:strokeColor="#FFFFFF"
            android:strokeWidth="1.5"
            android:strokeLineCap="round"/>
        
        <!-- Microphone stand -->
        <path
            android:pathData="M0,12L0,18"
            android:strokeColor="#138808"
            android:strokeWidth="3"
            android:strokeLineCap="round"/>
        
        <!-- Microphone base -->
        <path
            android:pathData="M-6,18L6,18"
            android:strokeColor="#138808"
            android:strokeWidth="3"
            android:strokeLineCap="round"/>
        
        <!-- Sound waves -->
        <path
            android:pathData="M15,-3C15,-3 18,-1 18,3C18,7 15,9 15,9"
            android:strokeColor="#FF9933"
            android:strokeWidth="2"
            android:strokeLineCap="round"
            android:fillColor="#00000000"/>
        <path
            android:pathData="M19,-6C19,-6 23,-3 23,3C23,9 19,12 19,12"
            android:strokeColor="#FF9933"
            android:strokeWidth="2"
            android:strokeLineCap="round"
            android:fillColor="#00000000"/>
        
        <!-- Hindi text indicator (simplified) -->
        <path
            android:pathData="M-20,-15L-15,-15L-15,-10L-20,-10Z"
            android:fillColor="#138808"/>
        <path
            android:pathData="M-18,-13L-17,-13L-17,-12L-18,-12Z"
            android:fillColor="#FFFFFF"/>
    </group>
</vector>
