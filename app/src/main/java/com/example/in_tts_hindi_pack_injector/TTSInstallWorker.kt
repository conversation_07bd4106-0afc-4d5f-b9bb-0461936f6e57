package com.example.in_tts_hindi_pack_injector

import android.content.Context
import android.os.Build
import android.os.Environment
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.pax.dal.ISys
import java.io.File
import java.io.IOException
import java.nio.file.Files

class TTSInstallWorker(private val appContext: Context, workerParams: WorkerParameters) :
    CoroutineWorker(appContext, workerParams) {

    companion object {
        private const val TAG = "TTSInstallWorker"
    }

    override suspend fun doWork(): Result {
        return try {
            // 根据 Android 版本选择合适的安装方法
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                installHindiTTSPack()
            } else {
                installHindiTTSPackLegacy()
            }
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error during TTS installation", e)
            Result.failure()
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun installHindiTTSPack() {
        val iSys: ISys? = App.get().dal?.getSys()
        if (iSys == null) {
            Log.e(TAG, "ISys is null, cannot update TTS voices.")
            return
        }

        val assetFileName = "hi-in_sign.zip"

        // 检查外部存储是否可用
        if (Environment.MEDIA_MOUNTED != Environment.getExternalStorageState()) {
            Log.e(TAG, "External storage is not mounted or not writable.")
            return
        }

        val externalStorageDir = Environment.getExternalStorageDirectory()
        val destFile = File(externalStorageDir, assetFileName)
        val zipFilePath = destFile.absolutePath

        // 检查文件是否已存在
        if (destFile.exists()) {
            Log.i(TAG, "TTS voice pack already exists at: $zipFilePath")
            // 文件已存在，直接尝试更新
        } else {
            if (!copyVoiceFileFromAssets(assetFileName, destFile)) {
                return // 复制失败则返回
            }
        }

        // 再次确认文件状态，然后尝试更新
        Log.i(TAG, "File path for TTS update: $zipFilePath")
        if (!destFile.exists()) {
            Log.e(
                TAG,
                "File does not exist after copy attempt (or was not found initially): $zipFilePath"
            )
            return
        }
        Log.i(TAG, "File exists: ${destFile.exists()}")
        Log.i(TAG, "File can be read: ${destFile.canRead()}")

        try {
            val result = iSys.updateTTSVoices(zipFilePath)
            // 根据result的值处理成功或失败的情况
            if (result == 0) {
                Log.i(TAG, "TTS voices updated successfully.")
            } else {
                Log.e(TAG, "Failed to update TTS voices, error code: $result")
            }
        } catch (e: Exception) { // 根据API文档，捕获通用异常
            Log.e(TAG, "Exception while updating TTS voices", e)
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun copyVoiceFileFromAssets(assetFileName: String, destFile: File): Boolean {
        // 文件不存在，从assets复制文件到外部存储 /storage/emulated/0/
        Log.i(TAG, "TTS voice pack not found, copying from assets to: ${destFile.absolutePath}")
        try {
            appContext.assets.open(assetFileName).use { inputStream ->
                Files.newOutputStream(destFile.toPath()).use { outputStream ->
                    val buffer = ByteArray(1024)
                    var length: Int
                    while (inputStream.read(buffer).also { length = it } > 0) {
                        outputStream.write(buffer, 0, length)
                    }
                    outputStream.flush()
                }
            }
            Log.i(TAG, "TTS voice pack copied to external storage: ${destFile.absolutePath}")
            return true
        } catch (e: IOException) {
            Log.e(TAG, "Failed to copy TTS voice pack from assets to external storage", e)
            return false
        }
    }

    private fun installHindiTTSPackLegacy() {
        val iSys: ISys? = App.get().dal?.getSys()
        if (iSys == null) {
            Log.e(TAG, "ISys is null, cannot update TTS voices.")
            return
        }

        val assetFileName = "hi-in_sign.zip"

        // 检查外部存储是否可用
        if (Environment.MEDIA_MOUNTED != Environment.getExternalStorageState()) {
            Log.e(TAG, "External storage is not mounted or not writable.")
            return
        }

        val externalStorageDir = Environment.getExternalStorageDirectory()
        val destFile = File(externalStorageDir, assetFileName)
        val zipFilePath = destFile.absolutePath

        // 检查文件是否已存在
        if (destFile.exists()) {
            Log.i(TAG, "TTS voice pack already exists at: $zipFilePath")
        } else {
            if (!copyVoiceFileFromAssetsLegacy(assetFileName, destFile)) {
                return
            }
        }

        // 再次确认文件状态，然后尝试更新
        Log.i(TAG, "File path for TTS update: $zipFilePath")
        if (!destFile.exists()) {
            Log.e(TAG, "File does not exist after copy attempt: $zipFilePath")
            return
        }

        try {
            val result = iSys.updateTTSVoices(zipFilePath)
            if (result == 0) {
                Log.i(TAG, "TTS voices updated successfully.")
            } else {
                Log.e(TAG, "Failed to update TTS voices, error code: $result")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception while updating TTS voices", e)
        }
    }

    private fun copyVoiceFileFromAssetsLegacy(assetFileName: String, destFile: File): Boolean {
        Log.i(TAG, "TTS voice pack not found, copying from assets to: ${destFile.absolutePath}")
        try {
            appContext.assets.open(assetFileName).use { inputStream ->
                destFile.outputStream().use { outputStream ->
                    val buffer = ByteArray(1024)
                    var length: Int
                    while (inputStream.read(buffer).also { length = it } > 0) {
                        outputStream.write(buffer, 0, length)
                    }
                    outputStream.flush()
                }
            }
            Log.i(TAG, "TTS voice pack copied to external storage: ${destFile.absolutePath}")
            return true
        } catch (e: IOException) {
            Log.e(TAG, "Failed to copy TTS voice pack from assets to external storage", e)
            return false
        }
    }
}