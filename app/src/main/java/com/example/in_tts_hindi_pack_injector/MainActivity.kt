package com.example.in_tts_hindi_pack_injector

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.work.*
import com.example.in_tts_hindi_pack_injector.ui.theme.IN_TTS_Hindi_Pack_InjectorTheme
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private var installationStatus by mutableStateOf("Preparing installation...")
    private var isInstalling by mutableStateOf(true)
    private var installationResult by mutableStateOf<Boolean?>(null)

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            startInstallation()
        } else {
            installationStatus = "Permission denied, cannot install voice pack"
            isInstalling = false
            installationResult = false
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            IN_TTS_Hindi_Pack_InjectorTheme {
                InstallationScreen(
                    status = installationStatus,
                    isInstalling = isInstalling,
                    result = installationResult,
                    onExit = { finishAndRemoveTask() }
                )
            }
        }

        // 检查权限并开始安装
        checkPermissionsAndStart()
    }

    private fun checkPermissionsAndStart() {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }

        val allPermissionsGranted = permissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }

        if (allPermissionsGranted) {
            startInstallation()
        } else {
            requestPermissionLauncher.launch(permissions)
        }
    }

    private fun startInstallation() {
        installationStatus = "Installing Hindi voice pack..."

        // 创建工作请求
        val workRequest = OneTimeWorkRequestBuilder<TTSInstallWorker>()
            .build()

        // 启动工作
        val workManager = WorkManager.getInstance(this)
        workManager.enqueue(workRequest)

        // 观察工作状态
        lifecycleScope.launch {
            workManager.getWorkInfoByIdLiveData(workRequest.id).observe(this@MainActivity) { workInfo ->
                when (workInfo?.state) {
                    WorkInfo.State.RUNNING -> {
                        installationStatus = "Installing Hindi voice pack..."
                        isInstalling = true
                    }
                    WorkInfo.State.SUCCEEDED -> {
                        isInstalling = false
                        installationResult = true
                        installationStatus = "Hindi voice pack installed successfully!"
                        Toast.makeText(
                            this@MainActivity,
                            installationStatus,
                            Toast.LENGTH_LONG
                        ).show()
                    }
                    WorkInfo.State.FAILED -> {
                        isInstalling = false
                        installationResult = false
                        installationStatus = "Hindi voice pack installation failed, please check logs"
                        Toast.makeText(
                            this@MainActivity,
                            installationStatus,
                            Toast.LENGTH_LONG
                        ).show()
                    }
                    else -> {
                        // 其他状态（ENQUEUED, BLOCKED, CANCELLED）
                        Log.d(TAG, "Work state: ${workInfo?.state}")
                    }
                }
            }
        }
    }
}

@Composable
fun InstallationScreen(
    status: String,
    isInstalling: Boolean,
    result: Boolean?,
    onExit: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 标题
            Text(
                text = "Hindi TTS Voice Pack Installer",
                style = MaterialTheme.typography.headlineMedium,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // 状态图标
            when {
                isInstalling -> {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(64.dp)
                            .padding(bottom = 24.dp),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                result == true -> {
                    Text(
                        text = "✅",
                        style = MaterialTheme.typography.displayLarge,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                }
                result == false -> {
                    Text(
                        text = "❌",
                        style = MaterialTheme.typography.displayLarge,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                }
            }

            // 状态文本
            Text(
                text = status,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // 退出按钮（仅在安装完成后显示）
            if (!isInstalling) {
                Button(
                    onClick = onExit,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                ) {
                    Text("Exit App")
                }
            }
        }
    }
}